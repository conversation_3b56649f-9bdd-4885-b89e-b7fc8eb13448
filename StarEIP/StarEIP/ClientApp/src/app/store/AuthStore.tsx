import { create } from "zustand";

// Remember to keep this type in sync with UserPermission.cs in the back-end!
export type UserPermission =
  | "ViewDashboard"
  | "ManageUsers"
  | "ViewChildren"
  | "ViewAuthorizations"
  | "AllowManageAuthorization"
  | "ViewFaxes"
  | "ViewEmailTemplates"
  | "ViewPhysicians"
  | "ViewAllAuth"
  | "ViewReports"
  | "AllowReportDesigner"
  | "AllowManageContacts"
  | "AllowDataImport"
  | "ViewRejections"
  | "ViewAuditLogs"
  | "ViewTasks"
  | "ImpersonateUsers";

// Update the store type to use UserPermission
const useAuthStore = create<{
  permissions: UserPermission[];
  setPermissions: (permissions: UserPermission[]) => void;
  originalUser: { id: number; token: string; permissions: UserPermission[] } | null;
  setOriginalUser: (user: { id: number; token: string; permissions: UserPermission[] } | null) => void;
}>((set) => ({
  permissions: [],
  setPermissions: (permissions) => set({ permissions }),
  originalUser: null,
  setOriginalUser: (user) => set({ originalUser: user }),
}));

export const useHasPermission = (permission: UserPermission) =>
  useAuthStore((s) => s.permissions.some((p) => p === permission));

export default useAuthStore;
