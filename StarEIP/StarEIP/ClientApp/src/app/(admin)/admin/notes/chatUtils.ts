import { Note } from "../../../../../types/Child";
import { ChatTypes } from "devextreme-react/chat";

/**
 * Transforms the application's Note objects into the format expected by DevExtreme Chat
 */
export const transformNotesToChatMessages = (notes: Note[]): ChatTypes.Message[] => {
  return notes.map(note => ({
    timestamp: new Date(note.createdOn).getTime(),
    author: {
      id: note.id.toString(), // Using note ID as author ID since we don't have user IDs
      name: note.createdBy,
      // We could add avatarUrl here if available
    },
    text: note.note,
  }));
};

/**
 * Creates a user object for the current user
 */
export const getCurrentUser = (userName: string): ChatTypes.User => {
  return {
    id: 'current-user', // This would ideally be the actual user ID
    name: userName,
  };
};

/**
 * Gets initials from a name (e.g., "<PERSON>" -> "JD")
 */
export const getInitials = (name: string): string => {
  if (!name) return "";

  return name
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

/**
 * Generates a random color based on a name
 */
export const getRandomColor = (name: string): string => {
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  const hue = hash % 360;
  return `hsl(${hue}, 70%, 60%)`;
};

