/* DevExtreme Chat component styling */
.dx-chat {
  height: 100%;
  border-radius: 8px;
  border: 1px solid var(--mantine-color-gray-3);
  overflow: hidden;
}

.dx-chat-message {
  margin: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  max-width: 80%;
}

.dx-chat-message-current-user {
  background-color: var(--mantine-color-teal-1);
  align-self: flex-end;
}

.dx-chat-message-other-user {
  background-color: var(--mantine-color-gray-1);
  align-self: flex-start;
}

.dx-chat-message-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.dx-chat-message-timestamp {
  font-size: 0.75rem;
  color: var(--mantine-color-gray-6);
  margin-top: 4px;
}

.dx-chat-message-author {
  font-weight: 500;
  margin-bottom: 4px;
}

.dx-chat-input {
  border-top: 1px solid var(--mantine-color-gray-3);
  padding: 8px;
}

.dx-avatar {
  border: 1px solid var(--mantine-color-gray-3);
}

