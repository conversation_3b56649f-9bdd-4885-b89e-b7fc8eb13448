import React, { useEffect, useState } from "react";
import { Note } from "../../../../../types/Child";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import { notifications } from "@mantine/notifications";
import {
  ActionIcon,
  Avatar,
  Button,
  Divider,
  Group,
  Stack,
  Text,
  Textarea,
  Tooltip,
} from "@mantine/core";
import { IconClock, IconSend, IconTrash } from "@tabler/icons-react";
import Chat, { MessageEnteredEvent } from "devextreme-react/chat";
import { transformNotesToChatMessages } from "./chatUtils";
import "./devextreme-chat.css";

interface NotesProps {
  entityType: "child" | "physician" | "task" | "authorization";
  entityId: number;
  onNoteAdded?: () => void;
  onNoteDeleted?: () => void;
  enableDelete?: boolean;
  maxLength?: number;
}

const serviceUrl = urlHelpers.getAbsoluteURL("api/notes");

const getInitials = (name: string) => {
  if (!name) return "";

  return name
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

const getRandomColor = (name: string) => {
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  const hue = hash % 360;
  return `hsl(${hue}, 70%, 60%)`;
};

const formatTimeAgo = (date: string) => {
  const now = new Date();
  const noteDate = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - noteDate.getTime()) / 1000);

  if (diffInSeconds < 60) return "just now";
  if (diffInSeconds < 3600)
    return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400)
    return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 604800)
    return `${Math.floor(diffInSeconds / 86400)} days ago`;

  return noteDate.toLocaleString();
};

export const NotesSection: React.FC<NotesProps> = ({
  entityType,
  entityId,
  onNoteAdded,
  onNoteDeleted,
  enableDelete = true,
  maxLength = 500,
}) => {
  const [notes, setNotes] = useState<Note[]>([]);
  const [newNote, setNewNote] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentUser, setCurrentUser] = useState({
    id: "current-user",
    name: "Current User",
  });

  const fetchNotes = async () => {
    setIsLoading(true);
    try {
      const response = await axios.get(
        `${serviceUrl}/${entityType}/${entityId}`,
      );
      setNotes(response.data);
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Error fetching notes",
        color: "red",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (entityId) {
      fetchNotes();
    }
  }, [entityId, entityType]);

  // Get current user info
  useEffect(() => {
    // In a real implementation, this would get the current user from the auth system
    // For now, we'll just use a placeholder
    const userName = "Current User"; // This would come from your auth system
    setCurrentUser({
      id: "current-user",
      name: userName,
    });
  }, []);

  const handleAddNote = async () => {
    if (!newNote.trim() || isSubmitting) return;
    setIsSubmitting(true);

    try {
      const response = await axios.post(
        `${serviceUrl}/${entityType}/${entityId}`,
        {
          note: newNote,
        },
      );

      await fetchNotes();

      setNewNote("");
      onNoteAdded?.();

      notifications.show({
        title: "Success",
        message: "Note added successfully",
        color: "green",
      });
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Error adding note",
        color: "red",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteNote = async (noteId: number) => {
    try {
      await axios.delete(`${serviceUrl}/${noteId}`);

      setNotes((prev) => prev.filter((note) => note.id !== noteId));
      onNoteDeleted?.();

      notifications.show({
        title: "Success",
        message: "Note deleted successfully",
        color: "green",
      });
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Error deleting note",
        color: "red",
      });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && e.ctrlKey) {
      handleAddNote();
    }
  };

  const handleMessageEntered = ({ message }: MessageEnteredEvent) => {
    // Add the message to the UI
    // Note: The actual message will be added when we fetch notes after saving

    // Save to backend
    axios
      .post(`${serviceUrl}/${entityType}/${entityId}`, {
        note: message.text,
      })
      .then(() => {
        fetchNotes();
        onNoteAdded?.();
      })
      .catch((error) => {
        notifications.show({
          title: "Error",
          message: "Error adding note",
          color: "red",
        });
      });
  };

  return (
    <Chat
      user={currentUser}
      items={transformNotesToChatMessages(notes)}
      onMessageEntered={handleMessageEntered}
      height="100%"
    />
  );
};
