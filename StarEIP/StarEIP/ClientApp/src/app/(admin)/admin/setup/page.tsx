'use client';

import React from 'react';
import {
  Title,
  Container,
  Text,
  Stack,
  Paper,
  Anchor,
  Grid
} from '@mantine/core';
import Link from 'next/link';
import { useHasPermission, UserPermission } from '@/app/store/AuthStore';

interface SetupLinkProps {
  title: string;
  description?: string;
  link: string;
  permission?: UserPermission;
}

interface SetupCategoryProps {
  title: string;
  links: SetupLinkProps[];
}

const SetupLink: React.FC<SetupLinkProps> = ({ title, description, link }) => {
  return (
    <Anchor
      component={Link}
      href={link}
      size="sm"
      style={{
        display: 'block',
        padding: '8px 12px',
        borderRadius: '4px',
        textDecoration: 'none',
        color: 'inherit',
        transition: 'background-color 0.2s ease'
      }}
      className="setup-link"
    >
      <Text fw={500} size="sm">{title}</Text>
      {description && (
        <Text c="dimmed" size="xs" mt={2}>{description}</Text>
      )}
    </Anchor>
  );
};

// Component to handle individual link permission checking
const SetupLinkWithPermission: React.FC<SetupLinkProps> = ({ title, description, link, permission }) => {
  const hasPermission = permission ? useHasPermission(permission) : true;

  if (permission && !hasPermission) {
    return null;
  }

  return <SetupLink title={title} description={description} link={link} />;
};

const SetupCategory: React.FC<SetupCategoryProps> = ({ title, links }) => {
  return (
    <Paper p="md" withBorder>
      <Text fw={600} size="md" mb="sm" c="blue.7">
        {title}
      </Text>
      <Stack gap="xs">
        {links.map((link, index) => (
          <SetupLinkWithPermission key={index} {...link} />
        ))}
      </Stack>
    </Paper>
  );
};

const SetupPage: React.FC = () => {
  const setupCategories: SetupCategoryProps[] = [
    {
      title: 'Task Management',
      links: [
        {
          title: 'Task Status Setup',
          description: 'Configure task statuses for workflow management',
          link: '/admin/setup/task-status',
          permission: 'ViewTasks'
        },
        {
          title: 'Task Template Setup',
          description: 'Manage task templates for standardized workflows',
          link: '/admin/setup/task-templates',
          permission: 'ViewTasks'
        }
      ]
    },
    {
      title: 'Authorization Management',
      links: [
        {
          title: 'SC Status Setup',
          description: 'Configure Service Coordination status types',
          link: '/admin/setup/sc-status',
          permission: 'AllowManageAuthorization'
        }
      ]
    },
    {
      title: 'Communication',
      links: [
        {
          title: 'Email Templates Setup',
          description: 'Configure email templates for automated communications',
          link: '/admin/admin/emailTemplates',
          permission: 'ViewEmailTemplates'
        }
      ]
    },
    {
      title: 'Reporting',
      links: [
        {
          title: 'Reports Setup',
          description: 'Manage and configure system reports',
          link: '/admin/reports',
          permission: 'ViewReports'
        }
      ]
    },
    {
      title: 'User Management',
      links: [
        {
          title: 'User Management',
          description: 'Manage system users and permissions',
          link: '/admin/admin/users',
          permission: 'ManageUsers'
        }
      ]
    },
    {
      title: 'Data Management',
      links: [
        {
          title: 'Data Import',
          description: 'Import data from external sources',
          link: '/admin/imports',
          permission: 'AllowDataImport'
        },
        {
          title: 'Contact Management',
          description: 'Manage system contacts',
          link: '/admin/contacts',
          permission: 'AllowManageContacts'
        }
      ]
    },
    {
      title: 'System Monitoring',
      links: [
        {
          title: 'Audit Logs',
          description: 'View system audit logs and activity',
          link: '/admin/audit-logs',
          permission: 'ViewAuditLogs'
        },
        {
          title: 'Rejections',
          description: 'View and manage system rejections',
          link: '/admin/rejections',
          permission: 'ViewRejections'
        }
      ]
    }
  ];

  return (
    <Container fluid p="md">
      <Title order={1} mb="xl">System Setup</Title>
      <Text c="dimmed" mb="xl">
        Configure and manage various aspects of the system. Select a category below to access specific setup options.
      </Text>

      <Grid>
        {setupCategories.map((category, index) => (
          <Grid.Col key={index} span={{ base: 12, sm: 6, lg: 4 }}>
            <SetupCategory {...category} />
          </Grid.Col>
        ))}
      </Grid>
    </Container>
  );
};

export default SetupPage;

