import React from "react";
import { Stack, Text, Group, Tooltip } from "@mantine/core";
import { ChildDetailsDtoSchema } from "@/api/types";

interface ChildDetailsTabProps {
  child: ChildDetailsDtoSchema;
}

const InfoGroup: React.FC<{
  icon?: React.JSX.Element | undefined;
  label: string | undefined;
  value: string | null | undefined;
}> = ({ icon, label, value }) => (
  <Tooltip label={label}>
    <Group style={{ flex: "1", minWidth: 150 }}>
      {icon && icon}
      {!icon && (
        <Text
          fw={400}
          size="14px"
          style={{ fontFamily: "Roboto", lineHeight: "18px" }}
          c="dimmed"
        >
          {label}:
        </Text>
      )}
      <Text
        fw={500}
        size="14px"
        style={{
          fontFamily: "Roboto",
          lineHeight: "18px",
          color: "rgba(0, 0, 0, 0.44)",
        }}
      >
        {value}
      </Text>
    </Group>
  </Tooltip>
);

const addressIcon = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_3_164)">
      <path
        d="M5.99998 12L1.33331 14.6667V3.99999L5.99998 1.33333M5.99998 12L10.6666 14.6667M5.99998 12V1.33333M10.6666 14.6667L14.6666 12V1.33333L10.6666 3.99999M10.6666 14.6667V3.99999M10.6666 3.99999L5.99998 1.33333"
        stroke="black"
        strokeOpacity="0.24"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_3_164">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

const ChildDetailsTab: React.FC<ChildDetailsTabProps> = ({ child }) => {
  return (
   <Group gap="8px" wrap="wrap" align="flex-start">
      <InfoGroup
        icon={addressIcon}
        label="Address"
        value={child.fullAddress}
      />

      <Text size="sm" fw={500} mb="sm" c="dimmed">
        Referring Physician
      </Text>
      <Stack gap="xs">
        <InfoGroup label="Name" value={child.referringPhysicianName} />
        <InfoGroup label="Phone" value={child.physicianPhoneNumber} />
        <InfoGroup label="Email" value={child.physicianEmailAddress} />
      </Stack>
    </Group>
  );
};

export default ChildDetailsTab;
