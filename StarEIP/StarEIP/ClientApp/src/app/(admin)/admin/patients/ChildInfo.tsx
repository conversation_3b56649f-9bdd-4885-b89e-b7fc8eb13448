import React, { useEffect, useState } from "react";
import {
  Stack,
  Group,
  Title,
  Text,
  Divider,
  Select,
  rem,
  Tooltip,
  ActionIcon,
  Tabs,
  Box,
} from "@mantine/core";
import { usePatientStore } from "./PatientStore";
import { notifications } from "@mantine/notifications";
import { IconCheck, IconEdit } from "@tabler/icons-react";
import calculateAge from "@/app/Utils/dateUtils";
import { NotesSection } from "@/app/(admin)/admin/notes/NotesSection";
import { useRouter } from "next/navigation";
import CompactAuthorizationsTable from "@/app/(admin)/admin/patients/CompactAuthorizationsTable";
import { SmsSection } from "@/app/(admin)/admin/sms/SmsSection";
import { api } from "@/api/generated";
import PatientHoverCard from "./components/PatientHoverCard";
import { ChildDetailsDtoSchema } from "@/api/types";


interface ChildInfoProps {
  child: ChildDetailsDtoSchema | null;
  showAuthorizationsTab?: boolean;
}

const InfoGroup: React.FC<{
  icon?: React.JSX.Element | undefined;
  label: string | undefined;
  value: string | null | undefined;
}> = ({ icon, label, value }) => (
  <Tooltip label={label}>
    <Group style={{ flex: "1 1 150px", minWidth: 150 }}>
      {icon && icon}
      {!icon && (
        <Text
          fw={400}
          size="14px"
          style={{ fontFamily: "Roboto", lineHeight: "18px" }}
          c="dimmed"
        >
          {label}:
        </Text>
      )}
      <Text
        fw={500}
        size="14px"
        style={{
          fontFamily: "Roboto",
          lineHeight: "18px",
          color: "rgba(0, 0, 0, 0.44)",
        }}
      >
        {value}
      </Text>
    </Group>
  </Tooltip>
);

const ageIcon = (
  <svg
    width="18"
    height="21"
    viewBox="0 0 18 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.00283 14.9643C8.7828 14.9779 8.56214 14.9779 8.34211 14.9643C6.69766 14.711 5.20412 13.8609 4.14676 12.5762C3.08939 11.2916 2.5423 9.66245 2.60998 7.99999C2.60998 4.14285 5.47604 1 9.00283 1C12.5296 1 15.3957 4.14285 15.3957 7.99999C15.3973 9.00202 15.2001 9.99441 14.8153 10.9196C14.3528 12.0915 13.5544 13.1007 12.5202 13.8203C11.4861 14.5399 10.2624 14.9379 9.00283 14.9643ZM9.00283 1.85714C5.96711 1.85714 3.50283 4.59821 3.50283 7.96428C3.44474 9.40216 3.9148 10.8116 4.82437 11.9268C5.73393 13.0419 7.02011 13.7857 8.44033 14.0178C9.62033 14.1083 10.798 13.8157 11.7985 13.1836C12.799 12.5514 13.5689 11.6134 13.9939 10.5089C14.3326 9.69409 14.5057 8.81993 14.5028 7.93749C14.5028 4.59821 12.0385 1.85714 9.00283 1.85714Z"
      fill="#C2C2C2"
      stroke="#C2C2C2"
      stroke-width="0.25"
    />
    <path
      d="M10.4047 17H7.72611C7.65763 16.9992 7.5902 16.983 7.52884 16.9526C7.46749 16.9222 7.41379 16.8783 7.37174 16.8242C7.32969 16.7702 7.30038 16.7073 7.28599 16.6404C7.27159 16.5734 7.27249 16.5041 7.28861 16.4375L7.87789 14.3482C7.89372 14.2896 7.92094 14.2347 7.958 14.1866C7.99506 14.1385 8.04123 14.0981 8.09388 14.0679C8.14653 14.0376 8.20462 14.018 8.26484 14.0102C8.32506 14.0024 8.38623 14.0065 8.44486 14.0223C8.50348 14.0382 8.55842 14.0654 8.60652 14.1024C8.65463 14.1395 8.69496 14.1857 8.72523 14.2383C8.75549 14.291 8.77508 14.3491 8.7829 14.4093C8.79071 14.4695 8.78658 14.5307 8.77075 14.5893L8.34218 16.1072H9.79753L9.25289 14.6429C9.21471 14.5302 9.22121 14.4072 9.27105 14.2993C9.32089 14.1913 9.41027 14.1065 9.52075 14.0625C9.63215 14.0247 9.75387 14.0315 9.86039 14.0814C9.96692 14.1313 10.05 14.2206 10.0922 14.3304L10.7886 16.4018C10.8151 16.4692 10.8241 16.5422 10.8147 16.6141C10.8054 16.6859 10.7779 16.7542 10.735 16.8125C10.6976 16.8666 10.6485 16.9115 10.5913 16.944C10.5341 16.9764 10.4703 16.9956 10.4047 17Z"
      fill="#C2C2C2"
      stroke="#C2C2C2"
      stroke-width="0.25"
    />
    <path
      d="M9.19638 16.9522L9.05205 17.2239C8.59883 18.077 8.8202 19.1322 9.57808 19.7312L9.82651 19.9276"
      stroke="#C2C2C2"
      stroke-linecap="round"
    />
    <path
      d="M7.44176 4.12747L7.14655 4.33208C6.29435 4.92275 5.70481 5.82127 5.50224 6.83818L5.2962 7.87253"
      stroke="#C2C2C2"
      stroke-linecap="round"
    />
  </svg>
);

const langIcon = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.33331 5.33334L6.66665 8.66668M2.66665 9.33334L6.66665 5.33334L7.99998 3.33334M1.33331 3.33334H9.33331M4.66665 1.33334H5.33331M8.60868 11.3333H13.3913M8.60868 11.3333L7.33331 14M8.60868 11.3333L10.5188 7.33936C10.6728 7.01752 10.7497 6.85661 10.855 6.80576C10.9466 6.76153 11.0534 6.76153 11.1449 6.80576C11.2502 6.85661 11.3272 7.01753 11.4811 7.33936L13.3913 11.3333M13.3913 11.3333L14.6666 14"
      stroke="black"
      stroke-opacity="0.24"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

const phoneIcon = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.58685 5.90214C6.05085 6.86856 6.68337 7.77432 7.48443 8.57537C8.28548 9.37642 9.19124 10.0089 10.1577 10.4729C10.2408 10.5129 10.2823 10.5328 10.3349 10.5481C10.5218 10.6026 10.7513 10.5635 10.9096 10.4502C10.9542 10.4183 10.9923 10.3802 11.0685 10.3039C11.3016 10.0709 11.4181 9.95434 11.5353 9.87815C11.9772 9.59082 12.5469 9.59082 12.9889 9.87815C13.106 9.95434 13.2226 10.0709 13.4556 10.3039L13.5856 10.4339C13.9398 10.7882 14.117 10.9653 14.2132 11.1556C14.4046 11.5339 14.4046 11.9808 14.2132 12.3591C14.117 12.5494 13.9399 12.7265 13.5856 13.0808L13.4805 13.1859C13.1274 13.539 12.9508 13.7156 12.7108 13.8504C12.4445 14 12.0308 14.1076 11.7253 14.1067C11.45 14.1058 11.2619 14.0524 10.8856 13.9456C8.86333 13.3717 6.95509 12.2887 5.36311 10.6967C3.77112 9.1047 2.68814 7.19646 2.11416 5.1742C2.00735 4.7979 1.95395 4.60975 1.95313 4.33445C1.95222 4.02897 2.0598 3.61531 2.20941 3.34897C2.34424 3.10895 2.52078 2.93241 2.87386 2.57933L2.97895 2.47424C3.33325 2.11995 3.5104 1.9428 3.70065 1.84657C4.07903 1.65519 4.52587 1.65519 4.90424 1.84657C5.0945 1.9428 5.27164 2.11995 5.62594 2.47424L5.75585 2.60415C5.98892 2.83722 6.10546 2.95376 6.18165 3.07094C6.46898 3.51287 6.46898 4.08259 6.18165 4.52452C6.10546 4.6417 5.98892 4.75824 5.75585 4.99131C5.67964 5.06752 5.64154 5.10562 5.60965 5.15016C5.4963 5.30845 5.45717 5.53796 5.51165 5.72486C5.52698 5.77745 5.54694 5.81902 5.58685 5.90214Z"
      stroke="black"
      stroke-opacity="0.24"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

const emailIcon = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.3333 12L9.90474 7.99999M6.09522 7.99999L1.66667 12M1.33331 4.66666L6.77659 8.47695C7.21737 8.7855 7.43776 8.93977 7.67749 8.99953C7.88924 9.05231 8.11072 9.05231 8.32247 8.99953C8.5622 8.93977 8.78259 8.7855 9.22337 8.47695L14.6666 4.66666M4.53331 13.3333H11.4666C12.5868 13.3333 13.1468 13.3333 13.5746 13.1153C13.951 12.9236 14.2569 12.6176 14.4487 12.2413C14.6666 11.8135 14.6666 11.2534 14.6666 10.1333V5.86666C14.6666 4.74655 14.6666 4.1865 14.4487 3.75868C14.2569 3.38235 13.951 3.07639 13.5746 2.88464C13.1468 2.66666 12.5868 2.66666 11.4666 2.66666H4.53331C3.41321 2.66666 2.85316 2.66666 2.42533 2.88464C2.04901 3.07639 1.74305 3.38235 1.5513 3.75868C1.33331 4.1865 1.33331 4.74655 1.33331 5.86666V10.1333C1.33331 11.2534 1.33331 11.8135 1.5513 12.2413C1.74305 12.6176 2.04901 12.9236 2.42533 13.1153C2.85316 13.3333 3.41321 13.3333 4.53331 13.3333Z"
      stroke="black"
      stroke-opacity="0.24"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

const parentIcon = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      cx="8"
      cy="5"
      r="3"
      stroke="black"
      strokeOpacity="0.24"
      strokeWidth="1.2"
    />
    <path
      d="M2.5 13C2.5 10.7909 4.29086 9 6.5 9H9.5C11.7091 9 13.5 10.7909 13.5 13"
      stroke="black"
      strokeOpacity="0.24"
      strokeWidth="1.2"
      strokeLinecap="round"
    />
  </svg>
);

const addressIcon = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_3_164)">
      <path
        d="M5.99998 12L1.33331 14.6667V3.99999L5.99998 1.33333M5.99998 12L10.6666 14.6667M5.99998 12V1.33333M10.6666 14.6667L14.6666 12V1.33333L10.6666 3.99999M10.6666 14.6667V3.99999M10.6666 3.99999L5.99998 1.33333"
        stroke="black"
        strokeOpacity="0.24"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_3_164">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);



const ChildInfo: React.FC<ChildInfoProps> = ({
  child,
  showAuthorizationsTab,
}) => {
  const { dataGridRef } = usePatientStore();
  const [status, setStatus] = useState<string | undefined>(
    child?.status ?? undefined,
  );
  const [statuses, setStatuses] = useState<string[]>([]);
  const router = useRouter();

  useEffect(() => {
    setStatus(child?.status ?? undefined);
  }, [child]);

  useEffect(() => {
    const fetchStatuses = async () => {
      try {
        //const response = await axios.get(`${serviceUrlChildren}/status`);
        const response = await api.children.getApiChildrenStatus();
        let statusList = response.data
          .sort((a, b) => a.sortOrder - b.sortOrder)
          .map((item) => item.status);
        setStatuses(
          statusList.filter(
            (status): status is string =>
              status !== null && status !== undefined,
          ),
        );
      } catch (error) {
        console.error("Error fetching statuses:", error);
      }
    };

    fetchStatuses();
  }, [child]);

  if (!child) {
    return <div>No Child Selected</div>;
  }

  const updateStatus = async (status: string) => {
    try {
      console.log("Updating status to:", status);
      await api.children.postApiChildrenStatus({
        childId: child.id,
        status: status,
      });
      child.status = status;
      setStatus(status);
      notifications.show({
        title: "Status updated",
        message: `Status updated to ${status}`,
        icon: <IconCheck style={{ width: rem(18), height: rem(18) }} />,
        loading: false,
        autoClose: 2000,
      });
      const grid = dataGridRef.current.instance();
      if (grid) {
        const index = grid.getRowIndexByKey(child.id);
        grid.repaint();
        grid.repaintRows([index]);
      }
    } catch (error) {
      console.error("Error updating status:", error);
    }
  };

  const handleEdit = () => {
    router.push(`/admin/patients/${child.id}/edit`);
  };

  return (
    <Stack gap="1" style={{ height: "100%", overflowY: "auto" }}>
      <Box m="md">
        <Group justify="space-between">
          <PatientHoverCard childDetails={child} position="bottom">
            <Title order={4}>
              {child.firstName} {child.lastName}
            </Title>
          </PatientHoverCard>
          <Select
            value={status}
            onChange={(value) => updateStatus(value as string)}
            color="dimmed"
            data={statuses}
            allowDeselect={false}
            variant="filled"
            size="xs"
            radius="xl"
          />
        </Group>


      </Box>

      <Tabs
        defaultValue="details"
        id="childInfoDetailsTabs"
        styles={{
          root: {
            display: "flex",
            flexDirection: "column",
            flex: 1,
            overflow: "auto",
          },
        }}
      >
        <Tabs.List>
          <Tabs.Tab value="details">Details</Tabs.Tab>
          <Tabs.Tab value="notes">Notes ({child.notesCount})</Tabs.Tab>
          {showAuthorizationsTab && (
            <Tabs.Tab value="auth">Authorizations ({child.authorizationsCount})</Tabs.Tab>
          )}
          <Tabs.Tab value="sms">SMS</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="details">
          <Stack gap="lg" p="md" style={{ maxWidth: "600px" }}>
            <div>
              <Text size="sm" fw={500} mb="sm" c="dimmed">
                Child Information
              </Text>
              <Group gap="8px" wrap="wrap" align="flex-start">
                <InfoGroup
                  icon={ageIcon}
                  label="Date of Birth"
                  value={
                    child.dateOfBirth
                      ? `Age ${calculateAge(child.dateOfBirth)} (${new Date(child.dateOfBirth).toLocaleDateString()})`
                      : ""
                  }
                />
                <InfoGroup
                  icon={langIcon}
                  label="Primary Language"
                  value={child.primaryLanguage}
                />
              </Group>
            </div>

            <div>
              <Text size="sm" fw={500} mb="sm" c="dimmed">
                Parent Information
              </Text>
              <Group gap="8px" wrap="wrap" align="flex-start">
                <InfoGroup
                  icon={parentIcon}
                  label="Parent Name"
                  value={child.parentName}
                />
                <InfoGroup
                  icon={phoneIcon}
                  label="Phone"
                  value={child.parentPhoneNumber}
                />
                <InfoGroup icon={emailIcon} label="Email" value={child.parentEmail} />
              </Group>
            </div>

            <div>
              <Text size="sm" fw={500} mb="sm" c="dimmed">
                Address
              </Text>
              <InfoGroup
                icon={addressIcon}
                label="Address"
                value={child.fullAddress}
              />
            </div>

            <div>
              <Text size="sm" fw={500} mb="sm" c="dimmed">
                Referring Physician
              </Text>
              <Stack gap="xs">
                <InfoGroup label="Name" value={child.referringPhysicianName} />
                <InfoGroup label="Phone" value={child.physicianPhoneNumber} />
                <InfoGroup label="Email" value={child.physicianEmailAddress} />
              </Stack>
            </div>
          </Stack>
        </Tabs.Panel>

        {child.id && (
          <Tabs.Panel
            value="notes"
            style={{
              height: "90%",
              display: "flex",
              flex: 1,
              flexGrow: 1,
              flexDirection: "column",
            }}
          >
            <NotesSection
              entityType="child"
              entityId={child.id}
              // onNoteAdded={() => setChildDetails(prev => prev ? { ...prev, notesCount: prev.notesCount + 1 } : null)}
              // onNoteDeleted={() => setChildDetails(prev => prev ? { ...prev, notesCount: prev.notesCount - 1 } : null)}
              maxLength={500}
              enableDelete={true}
            />
          </Tabs.Panel>
        )}
        {child.id && (
          <>
            {showAuthorizationsTab && (
              <Tabs.Panel
                value="auth"
                style={{
                  height: "100%",
                  display: "flex",
                  flex: 1,
                  flexGrow: 1,
                  flexDirection: "column",
                }}
              >
                <CompactAuthorizationsTable childId={child.id} />
              </Tabs.Panel>
            )}
            <Tabs.Panel
              value="sms"
              style={{
                height: "100%",
                display: "flex",
                flex: 1,
                flexGrow: 1,
                flexDirection: "column",
                overflow: "auto",
              }}
            >
              <SmsSection
                phoneNumber={child.parentPhoneNumber ?? null}
                childId={child.id}
              />
            </Tabs.Panel>
          </>
        )}
      </Tabs>
    </Stack>
  );
};

export default ChildInfo;
