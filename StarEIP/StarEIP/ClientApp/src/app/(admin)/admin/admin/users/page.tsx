"use client";

import React, { useState, useRef, useEffect } from "react";
import axios, { AxiosError } from "axios";
import DataGrid, {
  Column,
  Pager,
  Paging,
  SearchPanel,
  RemoteOperations,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Editing,
  Toolbar,
  Item,
  Button as DgButton,
} from "devextreme-react/data-grid";
import { createStore, CustomStore } from "devextreme-aspnet-data-nojquery";
import urlHelpers from "@/app/urlHelpers";
import { useRouter } from "next/navigation";
import dataGridExport from "@/app/Utils/dataGridExport";
import { useDisclosure } from "@mantine/hooks";
import {
  Drawer,
  Group,
  Switch,
  Text,
  Loader,
  Button,
  Modal,
  Container,
  Stack,
  Divider,
  Badge,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import Form, { ButtonItem, Item as FormItem } from "devextreme-react/form";
import { IconX, IconCheck, IconRefresh, IconPlus, IconUserOff } from "@tabler/icons-react";
import useAuthStore, { UserPermission, useHasPermission } from "@/app/store/AuthStore";

const serviceUrl = urlHelpers.getAbsoluteURL("api/users");

interface User {
  id: number;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  sendInviteEmail: boolean;
}

interface Role {
  name: string;
  description: string;
}

const UsersPage = () => {
  const [remoteDataSource, setRemoteDataSource] = useState<CustomStore>();
  const router = useRouter();
  const dataGridRef = useRef(null);
  const [newUser, setNewUser] = useState<User>({
    id: 0,
    email: "",
    password: "",
    firstName: "",
    lastName: "",
    sendInviteEmail: true,
  });

  const [openedAddUser, { open: openAddUser, close: closeAddUser }] =
    useDisclosure(false);

  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // Permission related state
  const [
    openedManagePermissions,
    { open: openManagePermissions, close: closeManagePermissions },
  ] = useDisclosure(false);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [loadingPermissions, setLoadingPermissions] = useState(false);
  const [availablePermissions, setAvailablePermissions] = useState<
    { value: string; friendlyName: string }[]
  >([]);

  //reset password link related
  const [
    confirmDialogOpened,
    { open: openConfirmDialog, close: closeConfirmDialog },
  ] = useDisclosure(false);
  const [selectedUserForReset, setSelectedUserForReset] = useState<User | null>(
    null,
  );

  // Impersonation related state
  const [
    confirmImpersonateDialogOpened,
    { open: openConfirmImpersonateDialog, close: closeConfirmImpersonateDialog },
  ] = useDisclosure(false);
  const [selectedUserForImpersonate, setSelectedUserForImpersonate] = useState<User | null>(null);
  const [isImpersonating, setIsImpersonating] = useState(false);
  
  const setPermission = useAuthStore((state) => state.setPermissions);
  const setOriginalUser = useAuthStore((state) => state.setOriginalUser);
  const originalUser = useAuthStore((state) => state.originalUser);
  const canImpersonate = useHasPermission("ImpersonateUsers");

  const loadChildDataSource = async () => {
    setRemoteDataSource(
      createStore({
        key: "id",
        loadUrl: serviceUrl,
        updateUrl: `${serviceUrl}/update`,
        onBeforeSend: (e, s) => {
          s.headers = {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          };
        },
        errorHandler(e) {
          if (e.message === "Unauthorized") {
            router.push("/login");
          }
        },
      }),
    );
  };

  useEffect(() => {
    loadChildDataSource();
    
    // Check if we're currently impersonating a user
    const isImpersonating = !!originalUser;
    setIsImpersonating(isImpersonating);
  }, [originalUser]);

  const handleFocusedRowChanging = (e: any) => {
    // setSelectedChild(e.row && e.row.data);
    // if (onFocusedChildChanging) {
    //     onFocusedChildChanging(e.row.data);
    // }
  };

  const saveButtonOptions = {
    icon: "save",
    text: "Send Invitations",
    useSubmitBehavior: true,
    stylingMode: "contained",
    type: "default",
    width: 175,
  };

  const handleSave = async (e: React.FormEvent<HTMLFormElement>) => {
    try {
      e.preventDefault();
      await axios.post(`${serviceUrl}/create`, {
        ...newUser,
      });
      await loadChildDataSource();
      close();
    } catch (error) {
      const id = notifications.show({
        loading: false,
        title: "Error",
        message: `Error creating user: ${(error as Error)?.message}`,
        icon: <IconX />,
      });
    }
  };

  const openPermissionManager = async (user: User) => {
    setLoadingPermissions(true);
    setSelectedUser(user);

    try {
      // Fetch user's current permissions
      const response = await axios.get(`${serviceUrl}/${user.id}/claims`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
        },
      });
      setPermissions(response.data.map((claim: any) => claim.value));

      // Fetch all available permissions
      const permissionsResponse = await axios.get(
        `${urlHelpers.getAbsoluteURL("api/auth")}/permissions`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          },
        },
      );
      setAvailablePermissions(permissionsResponse.data);
    } catch (error) {
      notifications.show({
        loading: false,
        title: "Error",
        message: `Error fetching permissions for user: ${error}`,
        icon: <IconX />,
      });
    } finally {
      setLoadingPermissions(false);
      openManagePermissions();
    }
  };

  // Handle Adding or Removing Permission
  const handlePermissionChange = async (
    permissionValue: string,
    action: "add" | "remove",
  ) => {
    const url = `${serviceUrl}/${selectedUser?.id}/${action === "add" ? "addClaim" : "removeClaim"}`;
    try {
      await axios.post(
        url,
        { claimValue: permissionValue },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          },
        },
      );
      const updatedPermissions =
        action === "add"
          ? [...permissions, permissionValue]
          : permissions.filter((p) => p !== permissionValue);
      setPermissions(updatedPermissions);

      notifications.show({
        title: "Success",
        message: `Permission ${action === "add" ? "added" : "removed"} successfully`,
        icon: <IconCheck size={16} />,
        color: "teal",
        autoClose: 3000,
      });
    } catch (error) {
      notifications.show({
        title: "Error",
        message: `Error updating permissions for user: ${error}`,
        icon: <IconX size={16} />,
        color: "red",
        autoClose: 5000,
      });
    }
  };

  const handleInviteUser = async (e: React.FormEvent<HTMLFormElement>) => {
    try {
      e.preventDefault();
      await axios.post(`${serviceUrl}/invite`, newUser);
      notifications.show({
        title: "Success",
        message: `Invitation sent to ${newUser.email}`,
        icon: <IconCheck size={16} />,
        color: "teal",
        autoClose: 3000,
      });
      closeAddUser();
      setNewUser({
        id: 0,
        email: "",
        password: "",
        firstName: "",
        lastName: "",
        sendInviteEmail: false,
      });
      await loadChildDataSource();
    } catch (error) {
      notifications.show({
        title: "Error",
        message: `Error inviting user: ${(error as AxiosError)?.response?.data || error}`,
        icon: <IconX size={16} />,
        color: "red",
        autoClose: 5000,
      });
    }
  };

  const handleAddUserClose = () => {
    closeAddUser();
    setNewUser({
      id: 0,
      email: "",
      password: "",
      firstName: "",
      lastName: "",
      sendInviteEmail: false,
    });
  };

  const sendPasswordResetLink = async (user: User) => {
    setSelectedUserForReset(user);
    openConfirmDialog();
  };

  const confirmAndSendPasswordResetLink = async () => {
    if (!selectedUserForReset) return;

    try {
      const response = await axios.post(
        `${serviceUrl}/send-reset-password`,
        {
          email: selectedUserForReset.email,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          },
        },
      );

      notifications.show({
        title: "Success",
        message: `Password reset link sent to ${selectedUserForReset.email}`,
        icon: <IconCheck size={16} />,
        color: "teal",
        autoClose: 3000,
      });
      closeConfirmDialog();
    } catch (error) {
      notifications.show({
        title: "Error",
        message: `Error sending password reset link: ${error}`,
        icon: <IconX size={16} />,
        color: "red",
        autoClose: 5000,
      });
    }
  };

  // Impersonation functions
  const impersonateUser = async (user: User) => {
    setSelectedUserForImpersonate(user);
    openConfirmImpersonateDialog();
  };

  const confirmAndImpersonateUser = async () => {
    if (!selectedUserForImpersonate) return;

    try {
      // Store current user's token and permissions
      const currentToken = localStorage.getItem("jwtToken");
      const currentPermissions = useAuthStore.getState().permissions;
      const currentUserId = parseInt(
        JSON.parse(atob(currentToken!.split(".")[1])).nameid
      );

      // Call the impersonate endpoint
      const response = await axios.post(
        `${serviceUrl}/${selectedUserForImpersonate.id}/impersonate`,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${currentToken}`,
          },
        }
      );

      // Save original user info
      setOriginalUser({
        id: currentUserId,
        token: currentToken!,
        permissions: currentPermissions,
      });

      // Set the new token and permissions
      localStorage.setItem("jwtToken", response.data.token);
      setPermission(response.data.permissions as UserPermission[]);

      notifications.show({
        title: "Success",
        message: `You are now impersonating ${selectedUserForImpersonate.firstName} ${selectedUserForImpersonate.lastName}`,
        icon: <IconCheck size={16} />,
        color: "teal",
        autoClose: 3000,
      });

      closeConfirmImpersonateDialog();
      
      // Redirect to dashboard
      router.push("/admin/dashboard");
    } catch (error) {
      notifications.show({
        title: "Error",
        message: `Error impersonating user: ${error}`,
        icon: <IconX size={16} />,
        color: "red",
        autoClose: 5000,
      });
    }
  };

  const stopImpersonating = () => {
    if (!originalUser) return;

    // Restore original user's token and permissions
    localStorage.setItem("jwtToken", originalUser.token);
    setPermission(originalUser.permissions);
    setOriginalUser(null);

    notifications.show({
      title: "Success",
      message: "You have returned to your original user account",
      icon: <IconCheck size={16} />,
      color: "teal",
      autoClose: 3000,
    });

    // Refresh the page
    router.push("/admin/admin/users");
  };

  return (
    <Container size="100%" px="xs" style={{ height: "100%" }}>
      {isImpersonating && (
        <Group mb="md" justify="space-between">
          <Badge color="red" size="lg">
            You are currently impersonating another user
          </Badge>
          <Button 
            color="red" 
            leftSection={<IconUserOff size="1rem" />}
            onClick={stopImpersonating}
          >
            Stop Impersonating
          </Button>
        </Group>
      )}
      
      <DataGrid
        remoteOperations
        ref={dataGridRef}
        dataSource={remoteDataSource}
        onExporting={dataGridExport}
        height="100%"
        width="100%"
        allowColumnReordering={true}
        allowColumnResizing={true}
        columnAutoWidth={true}
        showBorders={true}
        columnResizingMode={"widget"}
        showColumnLines={false}
        twoWayBindingEnabled
        rowAlternationEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        onFocusedRowChanged={handleFocusedRowChanging}
      >
        <Toolbar>
          <Item location="before">
            <Button
              leftSection={<IconRefresh size="1rem" />}
              onClick={loadChildDataSource}
            >
              Refresh
            </Button>
          </Item>
          <Item name="searchPanel" locateInMenu="auto" location="before" />
          <Item location="before">
            <Button
              leftSection={<IconPlus size="1rem" />}
              onClick={openAddUser}
            >
              Add User
            </Button>
          </Item>
          <Item name="groupPanel" locateInMenu="auto" location="before" />
          <Item name="exportButton" locateInMenu="auto" location="after" />
          <Item name="applyFilterButton" locateInMenu="auto" location="after" />
          <Item name="revertButton" locateInMenu="auto" location="after" />
          <Item name="saveButton" locateInMenu="auto" location="after" />
          <Item
            name="columnChooserButton"
            locateInMenu="auto"
            location="after"
          />
        </Toolbar>
        <RemoteOperations groupPaging={true} />
        <Editing allowUpdating mode="popup" />
        <ColumnFixing enabled={true} />
        <SearchPanel visible width={250} />
        <HeaderFilter visible />
        <ColumnChooser enabled />
        <Sorting mode="multiple" />
        <ColumnFixing />
        <Paging defaultPageSize={40} />
        <Pager showPageSizeSelector />
        <FilterPanel visible />
        <FilterBuilderPopup />
        <Export enabled={true} />

        <Column type="buttons" cssClass={"text-align-right button-column"}>
          <DgButton name="edit" icon="edit" />
          <DgButton
            text="Permissions"
            icon="group"
            onClick={(e) => {
              if (e.row) {
                openPermissionManager(e.row.data);
              }
            }}
          />
          <DgButton
            text="Send Password Reset Link"
            icon="key"
            onClick={(e) => {
              if (e.row) {
                sendPasswordResetLink(e.row.data);
              }
            }}
          />
          {canImpersonate && (
            <DgButton
              text="Impersonate"
              icon="user"
              onClick={(e) => {
                if (e.row) {
                  impersonateUser(e.row.data);
                }
              }}
            />
          )}
        </Column>

        <Column dataField="id" caption="ID" allowEditing={false} />
        <Column dataField="firstName" caption="First Name" />
        <Column dataField="lastName" caption="Last Name" />
        <Column dataField="email" caption="Email" allowEditing={false} />
        <Column dataField="userName" caption="User Name" allowEditing={false} />
        <Column
          dataField="emailConfirmed"
          caption="Email Confirmed"
          dataType="boolean"
        />
        <Column dataField="phoneNumber" caption="Phone Number" />
        <Column
          dataField="phoneNumberConfirmed"
          caption="Phone Number Confirmed"
          dataType="boolean"
        />
        <Column
          dataField="twoFactorEnabled"
          caption="Two Factor Enabled"
          dataType="boolean"
        />
        <Column
          dataField="lockoutEnabled"
          caption="Lockout Enabled"
          dataType="boolean"
        />
        <Column dataField="lockoutEnd" caption="Lockout End" dataType="date" />
        <Column
          dataField="accessFailedCount"
          caption="Access Failed Count"
          dataType="number"
        />
      </DataGrid>
      <Drawer
        opened={openedAddUser}
        onClose={handleAddUserClose}
        title="Add User"
        size="xl"
        position="right"
      >
        <form onSubmit={handleInviteUser}>
          <Form formData={newUser}>
            <FormItem dataField="firstName" editorType="dxTextBox" />
            <FormItem dataField="lastName" editorType="dxTextBox" />
            <FormItem dataField="email" editorType="dxTextBox" />
            <FormItem dataField="sendInviteEmail" editorType="dxCheckBox" />
            {/* @ts-ignore */}
            <ButtonItem buttonOptions={saveButtonOptions} />
          </Form>
        </form>
      </Drawer>
      <Drawer
        opened={openedManagePermissions}
        onClose={closeManagePermissions}
        title="Manage Permissions"
        size="md"
        position="right"
      >
        {loadingPermissions ? (
          <Loader size="lg" />
        ) : (
          <Stack>
            <Divider />
            {availablePermissions.map((permission) => (
              <Group
                key={permission.value}
                align="center"
                justify="space-between"
              >
                <Text>{permission.friendlyName}</Text>
                <Switch
                  checked={permissions.includes(permission.value)}
                  onChange={(e) =>
                    handlePermissionChange(
                      permission.value,
                      e.currentTarget.checked ? "add" : "remove",
                    )
                  }
                  color="brand"
                  style={{ cursor: "pointer" }}
                />
              </Group>
            ))}
          </Stack>
        )}
      </Drawer>
      <Modal
        opened={confirmDialogOpened}
        onClose={closeConfirmDialog}
        title="Confirm Password Reset Link"
        centered
      >
        <Text>
          Are you sure you want to send a password reset link to{" "}
          {selectedUserForReset?.email}?
        </Text>
        <Group mt="md">
          <Button onClick={closeConfirmDialog} variant="outline">
            Cancel
          </Button>
          <Button
            color="brand"
            variant="filled"
            type="submit"
            onClick={confirmAndSendPasswordResetLink}
          >
            Send Link
          </Button>
        </Group>
      </Modal>
      <Modal
        opened={confirmImpersonateDialogOpened}
        onClose={closeConfirmImpersonateDialog}
        title="Confirm User Impersonation"
        centered
      >
        <Text>
          Are you sure you want to impersonate{" "}
          {selectedUserForImpersonate?.firstName} {selectedUserForImpersonate?.lastName} ({selectedUserForImpersonate?.email})?
        </Text>
        <Text size="sm" mt="xs" color="dimmed">
          You will be logged in as this user and have access to their permissions. You can return to your account at any time.
        </Text>
        <Group mt="md">
          <Button onClick={closeConfirmImpersonateDialog} variant="outline">
            Cancel
          </Button>
          <Button
            color="brand"
            variant="filled"
            type="submit"
            onClick={confirmAndImpersonateUser}
          >
            Impersonate User
          </Button>
        </Group>
      </Modal>
    </Container>
  );
};

export default UsersPage;
