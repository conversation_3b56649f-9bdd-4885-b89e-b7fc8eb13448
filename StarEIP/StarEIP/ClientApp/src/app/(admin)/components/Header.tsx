"use client";

import { useMainStore } from "@/app/store/MainStore";
import { Group, Burger, Text, Badge, Button } from "@mantine/core";
import useAuthStore from "@/app/store/AuthStore";
import { useRouter } from "next/navigation";
import { IconUserOff } from "@tabler/icons-react";

const Header = () => {
  const collapsed = useMainStore((state) => state.sideMenuCollapsed);
  const setCollapsed = useMainStore((state) => state.setSideMenuCollapsed);
  const originalUser = useAuthStore((state) => state.originalUser);
  const setPermission = useAuthStore((state) => state.setPermissions);
  const setOriginalUser = useAuthStore((state) => state.setOriginalUser);
  const router = useRouter();

  const isImpersonating = !!originalUser;

  const stopImpersonating = () => {
    if (!originalUser) return;

    // Restore original user's token and permissions
    localStorage.setItem("jwtToken", originalUser.token);
    setPermission(originalUser.permissions);
    setOriginalUser(null);

    // Redirect to users page
    router.push("/admin/admin/users");
  };

  return (
    <Group h="100%" px="md" justify="space-between">
      <Group>
        <Burger
          opened={!collapsed}
          onClick={() => setCollapsed(!collapsed)}
          hiddenFrom="sm"
          size="sm"
        />
        <Text>Star EIP</Text>
      </Group>
      
      {isImpersonating && (
        <Group>
          <Badge color="red" size="lg">
            Impersonating User
          </Badge>
          <Button 
            color="red" 
            size="xs"
            leftSection={<IconUserOff size="0.8rem" />}
            onClick={stopImpersonating}
          >
            Return to Admin
          </Button>
        </Group>
      )}
    </Group>
  );
};

export default Header;
